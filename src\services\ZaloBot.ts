import { ZaloConfig } from '../types';
import { logger } from '../utils/logger';
import { zaloIntegrationService } from './ZaloIntegrationService';

// Declare require for Node.js
declare const require: any;

// Import zca-js - sử dụng require vì có thể có vấn đề với ES6 import
const { Zalo, ThreadType, Urgency, TextStyle } = require('zca-js');

export class ZaloBot {
  private config: ZaloConfig;
  private isInitialized = false;
  private zalo: any; // Zalo instance
  private api: any; // API instance

  constructor(config: ZaloConfig) {
    this.config = config;
  }

  async initialize(): Promise<void> {
    try {
      logger.info('Initializing Zalo Bot...');

      // Initialize Zalo instance
      this.zalo = new Zalo({
        selfListen: false,
      });

      // Login with cookie if provided
      if (this.config.cookie) {
        logger.info('Logging in with cookie...');
        this.api = await this.zalo.login(this.config.cookie, this.config.userAgent);
      } else {
        logger.info('No cookie provided. Using QR code login...');
        this.api = await this.zalo.loginQR();
      }

      // Set up message listener
      this.setupMessageListener();

      // Migrate existing login to database
      await this.migrateToDatabase();

      this.isInitialized = true;
      logger.info('Zalo Bot initialized successfully');
    } catch (error) {
      logger.error('Failed to initialize Zalo Bot:', error);
      throw error;
    }
  }

  /**
   * Migrate current login session to database
   */
  private async migrateToDatabase(): Promise<void> {
    try {
      logger.info('Migrating Zalo login to database...');

      // Get tenant ID and inbox ID from environment or config
      const tenantId = process.env.DEFAULT_TENANT_ID || this.config.tenantId;
      const chatwootInboxId = process.env.DEFAULT_CHATWOOT_INBOX_ID
        ? parseInt(process.env.DEFAULT_CHATWOOT_INBOX_ID)
        : this.config.chatwootInboxId;

      if (!tenantId) {
        logger.warn('No tenant ID provided, skipping database migration');
        return;
      }

      const result = await zaloIntegrationService.migrateExistingLogin(
        this.api,
        tenantId,
        chatwootInboxId
      );

      if (result.success) {
        logger.info('Successfully migrated login to database', {
          accountId: result.accountId,
          zaloUserId: result.zaloUserId,
          displayName: result.displayName
        });
      } else {
        logger.error('Failed to migrate login to database', { error: result.error });
      }

    } catch (error: any) {
      logger.error('Error during database migration', { error: error.message });
      // Don't throw error to prevent bot initialization failure
    }
  }

  private setupMessageListener(): void {
    // Set up message listener using zca-js
    const { listener } = this.api;

    listener.on('message', (message: any) => {
      this.handleMessage(message);
    });

    // Set up other event listeners if needed
    listener.on('reaction', (reaction: any) => {
      logger.info('Received reaction:', reaction);
    });

    listener.on('group_event', (event: any) => {
      logger.info('Received group event:', event);
    });

    // Start listening
    listener.start();
    logger.info('Message listener started');
  }

  private async handleMessage(message: any): Promise<void> {
    try {
      logger.info(`Received message from ${message.senderID}: ${message.data.content}`);

      // Skip messages sent by the bot itself
      if (message.isSelf) {
        return;
      }

      // Handle different message types
      const isPlainText = typeof message.data.content === 'string';

      if (isPlainText) {
        await this.handleTextMessage(message);
      } else {
        // Handle other message types (images, files, etc.)
        logger.info('Received non-text message:', message.data);
      }
    } catch (error) {
      logger.error('Error handling message:', error);
    }
  }

  protected async handleTextMessage(message: any): Promise<void> {
    const messageText = message.data.content;
    const threadId = message.threadId;
    const threadType = message.type;

    logger.info(`Processing text message: ${messageText}`);

    // Simple echo bot logic - you can customize this
    if (messageText.toLowerCase().includes('hello') || messageText.toLowerCase().includes('hi')) {
      await this.sendTextMessage(threadId, 'Xin chào! Tôi là bot Zalo. Tôi có thể giúp gì cho bạn?', threadType);
    } else if (messageText.toLowerCase().includes('help')) {
      await this.sendTextMessage(threadId, 'Tôi có thể:\n- Trả lời tin nhắn\n- Gửi sticker\n- Xử lý hình ảnh\nHãy thử gửi "hello" để bắt đầu!', threadType);
    } else {
      // Echo the message back
      await this.sendTextMessage(threadId, `Bạn vừa nói: ${messageText}`, threadType);
    }
  }

  async sendTextMessage(threadId: string, text: string, threadType?: any): Promise<void> {
    try {
      logger.info(`Sending text message to ${threadId}: ${text}`);

      // Send message using zca-js API
      const result = await this.api.sendMessage(text, threadId, threadType);

      if (result && result.message) {
        logger.info(`Message sent successfully with ID: ${result.message.msgId}`);
      } else {
        logger.info('Message sent successfully');
      }
    } catch (error) {
      logger.error('Failed to send message:', error);
      throw error;
    }
  }

  // Additional utility methods for enhanced functionality
  async sendSticker(threadId: string, stickerId: string, threadType?: any): Promise<void> {
    try {
      logger.info(`Sending sticker ${stickerId} to ${threadId}`);
      await this.api.sendSticker(stickerId, threadId, threadType);
      logger.info('Sticker sent successfully');
    } catch (error) {
      logger.error('Failed to send sticker:', error);
      throw error;
    }
  }

  async sendMessageWithStyle(threadId: string, text: string, styles: any[], threadType?: any): Promise<void> {
    try {
      logger.info(`Sending styled message to ${threadId}: ${text}`);

      const messageContent = {
        msg: text,
        styles: styles
      };

      await this.api.sendMessage(messageContent, threadId, threadType);
      logger.info('Styled message sent successfully');
    } catch (error) {
      logger.error('Failed to send styled message:', error);
      throw error;
    }
  }

  // Get bot's own ID
  async getOwnId(): Promise<string> {
    try {
      return await this.api.getOwnId();
    } catch (error) {
      logger.error('Failed to get own ID:', error);
      throw error;
    }
  }

  // Check if bot is initialized and ready
  isReady(): boolean {
    return this.isInitialized && this.api;
  }
}
