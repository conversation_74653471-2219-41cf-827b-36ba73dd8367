import axios from 'axios';
import { logger } from '../utils/logger';

// Test configuration
const BASE_URL = 'http://localhost:3355';
const TEST_TENANT_ID = '9e20b074-5c04-4241-b2bd-58c0499c1f86'; // Replace with your tenant ID
const TEST_CHATWOOT_INBOX_ID = 281;

interface TestResult {
  test: string;
  success: boolean;
  data?: any;
  error?: string;
  duration?: number;
}

class QRSystemTester {
  private results: TestResult[] = [];

  async runAllTests(): Promise<void> {
    logger.info('🧪 Starting QR System Tests...');
    
    try {
      // Test 1: Create QR code via new endpoint
      await this.testCreateQRCode();
      
      // Test 2: Create QR code via integration endpoint
      await this.testCreateQRCodeIntegration();
      
      // Test 3: List QR codes
      await this.testListQRCodes();
      
      // Test 4: Check QR status
      await this.testCheckQRStatus();
      
      // Test 5: Test health endpoints
      await this.testHealthEndpoints();
      
      // Print results
      this.printResults();
      
    } catch (error: any) {
      logger.error('Test suite failed', { error: error.message });
    }
  }

  private async testCreateQRCode(): Promise<void> {
    const startTime = Date.now();
    
    try {
      const response = await axios.post(`${BASE_URL}/api/qr-management/create`, {
        chatwoot_inbox_id: TEST_CHATWOOT_INBOX_ID,
        expires_in_minutes: 10,
        user_agent: 'QR-System-Tester/1.0'
      }, {
        headers: {
          'X-Tenant-ID': TEST_TENANT_ID,
          'Content-Type': 'application/json'
        }
      });

      this.results.push({
        test: 'Create QR Code (New Endpoint)',
        success: true,
        data: {
          account_id: response.data.data.account_id,
          session_id: response.data.data.session_id,
          qr_code_url: response.data.data.qr_code_url,
          status: response.data.data.status
        },
        duration: Date.now() - startTime
      });

      logger.info('✅ Create QR Code test passed', { 
        accountId: response.data.data.account_id,
        sessionId: response.data.data.session_id,
        qrUrl: response.data.data.qr_code_url
      });

    } catch (error: any) {
      this.results.push({
        test: 'Create QR Code (New Endpoint)',
        success: false,
        error: error.response?.data?.message || error.message,
        duration: Date.now() - startTime
      });

      logger.error('❌ Create QR Code test failed', { 
        error: error.response?.data || error.message 
      });
    }
  }

  private async testCreateQRCodeIntegration(): Promise<void> {
    const startTime = Date.now();
    
    try {
      const response = await axios.post(`${BASE_URL}/api/zalo-integration/create-qr`, {
        chatwoot_inbox_id: TEST_CHATWOOT_INBOX_ID,
        expires_in_minutes: 10
      }, {
        headers: {
          'X-Tenant-ID': TEST_TENANT_ID,
          'Content-Type': 'application/json'
        }
      });

      this.results.push({
        test: 'Create QR Code (Integration Endpoint)',
        success: true,
        data: {
          account_id: response.data.data.account_id,
          qr_code_url: response.data.data.qr_code_url,
          chatwoot_inbox_id: response.data.data.chatwoot_inbox_id
        },
        duration: Date.now() - startTime
      });

      logger.info('✅ Create QR Code Integration test passed', { 
        accountId: response.data.data.account_id,
        qrUrl: response.data.data.qr_code_url
      });

    } catch (error: any) {
      this.results.push({
        test: 'Create QR Code (Integration Endpoint)',
        success: false,
        error: error.response?.data?.message || error.message,
        duration: Date.now() - startTime
      });

      logger.error('❌ Create QR Code Integration test failed', { 
        error: error.response?.data || error.message 
      });
    }
  }

  private async testListQRCodes(): Promise<void> {
    const startTime = Date.now();
    
    try {
      const response = await axios.get(`${BASE_URL}/api/qr-management/list`, {
        headers: {
          'X-Tenant-ID': TEST_TENANT_ID
        },
        params: {
          limit: 10,
          offset: 0
        }
      });

      this.results.push({
        test: 'List QR Codes',
        success: true,
        data: {
          total_qr_codes: response.data.data.qr_codes.length,
          qr_codes: response.data.data.qr_codes.map((qr: any) => ({
            session_id: qr.session_id,
            status: qr.status,
            created_at: qr.created_at
          }))
        },
        duration: Date.now() - startTime
      });

      logger.info('✅ List QR Codes test passed', { 
        count: response.data.data.qr_codes.length 
      });

    } catch (error: any) {
      this.results.push({
        test: 'List QR Codes',
        success: false,
        error: error.response?.data?.message || error.message,
        duration: Date.now() - startTime
      });

      logger.error('❌ List QR Codes test failed', { 
        error: error.response?.data || error.message 
      });
    }
  }

  private async testCheckQRStatus(): Promise<void> {
    const startTime = Date.now();
    
    // First get a session ID from the list
    try {
      const listResponse = await axios.get(`${BASE_URL}/api/qr-management/list`, {
        headers: {
          'X-Tenant-ID': TEST_TENANT_ID
        },
        params: { limit: 1 }
      });

      if (listResponse.data.data.qr_codes.length === 0) {
        this.results.push({
          test: 'Check QR Status',
          success: false,
          error: 'No QR codes available to test status check',
          duration: Date.now() - startTime
        });
        return;
      }

      const sessionId = listResponse.data.data.qr_codes[0].session_id;
      
      const response = await axios.get(`${BASE_URL}/api/qr-management/status/${sessionId}`, {
        headers: {
          'X-Tenant-ID': TEST_TENANT_ID
        }
      });

      this.results.push({
        test: 'Check QR Status',
        success: true,
        data: {
          session_id: response.data.data.session_id,
          status: response.data.data.status,
          qr_image_url: response.data.data.qr_image_url,
          expires_at: response.data.data.expires_at
        },
        duration: Date.now() - startTime
      });

      logger.info('✅ Check QR Status test passed', { 
        sessionId: response.data.data.session_id,
        status: response.data.data.status
      });

    } catch (error: any) {
      this.results.push({
        test: 'Check QR Status',
        success: false,
        error: error.response?.data?.message || error.message,
        duration: Date.now() - startTime
      });

      logger.error('❌ Check QR Status test failed', { 
        error: error.response?.data || error.message 
      });
    }
  }

  private async testHealthEndpoints(): Promise<void> {
    const startTime = Date.now();
    
    try {
      // Test main health endpoint
      const healthResponse = await axios.get(`${BASE_URL}/health`);
      
      // Test integration health
      const integrationHealthResponse = await axios.get(`${BASE_URL}/api/zalo-integration/health`);

      this.results.push({
        test: 'Health Endpoints',
        success: true,
        data: {
          main_health: healthResponse.data,
          integration_health: integrationHealthResponse.data
        },
        duration: Date.now() - startTime
      });

      logger.info('✅ Health Endpoints test passed');

    } catch (error: any) {
      this.results.push({
        test: 'Health Endpoints',
        success: false,
        error: error.response?.data?.message || error.message,
        duration: Date.now() - startTime
      });

      logger.error('❌ Health Endpoints test failed', { 
        error: error.response?.data || error.message 
      });
    }
  }

  private printResults(): void {
    logger.info('\n📊 Test Results Summary:');
    logger.info('========================');
    
    const passed = this.results.filter(r => r.success).length;
    const failed = this.results.filter(r => !r.success).length;
    
    this.results.forEach(result => {
      const status = result.success ? '✅ PASS' : '❌ FAIL';
      const duration = result.duration ? `(${result.duration}ms)` : '';
      
      logger.info(`${status} ${result.test} ${duration}`);
      
      if (!result.success && result.error) {
        logger.info(`   Error: ${result.error}`);
      }
      
      if (result.success && result.data) {
        logger.info(`   Data: ${JSON.stringify(result.data, null, 2)}`);
      }
    });
    
    logger.info('========================');
    logger.info(`Total: ${this.results.length} | Passed: ${passed} | Failed: ${failed}`);
    
    if (failed === 0) {
      logger.info('🎉 All tests passed!');
    } else {
      logger.error(`⚠️  ${failed} test(s) failed`);
    }
  }
}

// Run tests if this file is executed directly
if (require.main === module) {
  const tester = new QRSystemTester();
  tester.runAllTests().catch(error => {
    logger.error('Test execution failed', { error: error.message });
    process.exit(1);
  });
}

export { QRSystemTester };
