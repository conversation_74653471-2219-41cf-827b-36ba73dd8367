#!/usr/bin/env node

/**
 * Test script for Zalo Multi-Account Integration
 * Usage: node scripts/test-integration.js
 */

const axios = require('axios');

// Configuration
const BASE_URL = process.env.API_BASE_URL || 'http://localhost:3355';
const TENANT_ID = process.env.TEST_TENANT_ID || 'your-tenant-uuid';
const INBOX_ID = process.env.TEST_INBOX_ID || 123;

// Colors for console output
const colors = {
  green: '\x1b[32m',
  red: '\x1b[31m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  reset: '\x1b[0m'
};

const log = {
  success: (msg) => console.log(`${colors.green}✅ ${msg}${colors.reset}`),
  error: (msg) => console.log(`${colors.red}❌ ${msg}${colors.reset}`),
  warning: (msg) => console.log(`${colors.yellow}⚠️  ${msg}${colors.reset}`),
  info: (msg) => console.log(`${colors.blue}ℹ️  ${msg}${colors.reset}`)
};

// HTTP client with default headers
const api = axios.create({
  baseURL: BASE_URL,
  headers: {
    'Content-Type': 'application/json',
    'X-Tenant-ID': TENANT_ID
  },
  timeout: 10000
});

// Test functions
async function testHealthCheck() {
  log.info('Testing health checks...');
  
  try {
    // System health
    const systemHealth = await api.get('/api/zalo-monitoring/health');
    log.success(`System health: ${systemHealth.data.data.status}`);
    
    // Integration health
    const integrationHealth = await api.get('/api/zalo-integration/health');
    log.success(`Integration health: ${integrationHealth.data.data.status}`);
    
    return true;
  } catch (error) {
    log.error(`Health check failed: ${error.message}`);
    return false;
  }
}

async function testSetDefaults() {
  log.info('Setting default values...');
  
  try {
    const response = await api.post('/api/zalo-integration/set-defaults', {
      tenant_id: TENANT_ID,
      chatwoot_inbox_id: parseInt(INBOX_ID)
    });
    
    log.success('Default values set successfully');
    return true;
  } catch (error) {
    log.error(`Failed to set defaults: ${error.message}`);
    return false;
  }
}

async function testIntegrationStatus() {
  log.info('Checking integration status...');
  
  try {
    const response = await api.get('/api/zalo-integration/status');
    const data = response.data.data;
    
    log.info(`Total accounts: ${data.total_accounts}`);
    log.info(`Active accounts: ${data.active_accounts}`);
    log.info(`Linked accounts: ${data.linked_accounts}`);
    log.info(`Integration ready: ${data.integration_ready}`);
    
    if (data.accounts.length > 0) {
      log.info('Existing accounts:');
      data.accounts.forEach(acc => {
        console.log(`  - ${acc.zalo_display_name} (${acc.zalo_user_id}) - ${acc.status}`);
      });
    }
    
    return true;
  } catch (error) {
    log.error(`Failed to get integration status: ${error.message}`);
    return false;
  }
}

async function testCreateQRLogin() {
  log.info('Testing QR login creation...');
  
  try {
    const response = await api.post('/api/zalo-integration/create-qr', {
      chatwoot_inbox_id: parseInt(INBOX_ID),
      expires_in_minutes: 10
    });
    
    const data = response.data.data;
    log.success('QR login created successfully');
    log.info(`Account ID: ${data.account_id}`);
    log.info(`QR Code URL: ${BASE_URL}${data.qr_code_url}`);
    log.info(`Expires in: ${data.expires_in_minutes} minutes`);
    
    // Test QR status check
    await testQRStatus(data.account_id);
    
    return data.account_id;
  } catch (error) {
    log.error(`Failed to create QR login: ${error.message}`);
    return null;
  }
}

async function testQRStatus(accountId) {
  log.info(`Checking QR status for account: ${accountId}`);
  
  try {
    const response = await api.get(`/api/zalo-auth/qr-status/${accountId}`);
    const data = response.data.data;
    
    log.info(`QR Status: ${data.status}`);
    
    if (data.account) {
      log.info(`Account status: ${data.account.status}`);
    }
    
    return true;
  } catch (error) {
    log.error(`Failed to check QR status: ${error.message}`);
    return false;
  }
}

async function testAccountList() {
  log.info('Testing account list...');
  
  try {
    const response = await api.get('/api/zalo-auth/accounts');
    const data = response.data.data;
    
    log.success(`Found ${data.accounts.length} accounts`);
    
    if (data.accounts.length > 0) {
      log.info('Account details:');
      data.accounts.forEach(acc => {
        console.log(`  - ID: ${acc.id}`);
        console.log(`    Name: ${acc.zalo_display_name}`);
        console.log(`    Status: ${acc.status}`);
        console.log(`    Chatwoot: ${acc.chatwoot_inbox_id || 'Not linked'}`);
        console.log(`    Health: ${acc.health ? (acc.health.is_healthy ? 'Healthy' : 'Unhealthy') : 'Unknown'}`);
        console.log('');
      });
    }
    
    return data.accounts;
  } catch (error) {
    log.error(`Failed to get account list: ${error.message}`);
    return [];
  }
}

async function testMonitoringDashboard() {
  log.info('Testing monitoring dashboard...');
  
  try {
    const response = await api.get('/api/zalo-monitoring/dashboard');
    const data = response.data.data;
    
    log.success('Dashboard data retrieved');
    log.info(`Overview:`);
    console.log(`  - Total accounts: ${data.overview.total_accounts}`);
    console.log(`  - Active accounts: ${data.overview.active_accounts}`);
    console.log(`  - Healthy connections: ${data.overview.healthy_connections}`);
    console.log(`  - Error accounts: ${data.overview.error_accounts}`);
    
    log.info(`Performance:`);
    console.log(`  - Average uptime: ${data.performance.averageUptime.toFixed(2)}%`);
    console.log(`  - Average response time: ${data.performance.averageResponseTime.toFixed(0)}ms`);
    console.log(`  - Recent activity: ${data.performance.recentActivity} accounts`);
    console.log(`  - Critical accounts: ${data.performance.criticalAccounts}`);
    
    return true;
  } catch (error) {
    log.error(`Failed to get dashboard data: ${error.message}`);
    return false;
  }
}

async function testReloadAccounts() {
  log.info('Testing account reload...');
  
  try {
    const response = await api.post('/api/zalo-integration/reload-accounts');
    const data = response.data.data;
    
    log.success('Account reload completed');
    log.info(`Loaded: ${data.loaded_count} accounts`);
    log.info(`Errors: ${data.error_count}`);
    
    if (data.errors.length > 0) {
      log.warning('Reload errors:');
      data.errors.forEach(error => {
        console.log(`  - ${error}`);
      });
    }
    
    return true;
  } catch (error) {
    log.error(`Failed to reload accounts: ${error.message}`);
    return false;
  }
}

// Main test runner
async function runTests() {
  console.log(`${colors.blue}🧪 Starting Zalo Integration Tests${colors.reset}`);
  console.log(`Base URL: ${BASE_URL}`);
  console.log(`Tenant ID: ${TENANT_ID}`);
  console.log(`Inbox ID: ${INBOX_ID}`);
  console.log('');
  
  const results = {
    passed: 0,
    failed: 0,
    total: 0
  };
  
  const tests = [
    { name: 'Health Check', fn: testHealthCheck },
    { name: 'Set Defaults', fn: testSetDefaults },
    { name: 'Integration Status', fn: testIntegrationStatus },
    { name: 'Account List', fn: testAccountList },
    { name: 'Monitoring Dashboard', fn: testMonitoringDashboard },
    { name: 'Reload Accounts', fn: testReloadAccounts },
    { name: 'Create QR Login', fn: testCreateQRLogin }
  ];
  
  for (const test of tests) {
    console.log(`\n${colors.yellow}📋 Running: ${test.name}${colors.reset}`);
    results.total++;
    
    try {
      const success = await test.fn();
      if (success) {
        results.passed++;
        log.success(`${test.name} passed`);
      } else {
        results.failed++;
        log.error(`${test.name} failed`);
      }
    } catch (error) {
      results.failed++;
      log.error(`${test.name} failed with error: ${error.message}`);
    }
    
    // Wait between tests
    await new Promise(resolve => setTimeout(resolve, 1000));
  }
  
  // Summary
  console.log(`\n${colors.blue}📊 Test Summary${colors.reset}`);
  console.log(`Total: ${results.total}`);
  console.log(`${colors.green}Passed: ${results.passed}${colors.reset}`);
  console.log(`${colors.red}Failed: ${results.failed}${colors.reset}`);
  
  if (results.failed === 0) {
    log.success('All tests passed! 🎉');
  } else {
    log.warning(`${results.failed} test(s) failed. Check the output above for details.`);
  }
  
  return results.failed === 0;
}

// Run tests if called directly
if (require.main === module) {
  runTests()
    .then(success => {
      process.exit(success ? 0 : 1);
    })
    .catch(error => {
      log.error(`Test runner failed: ${error.message}`);
      process.exit(1);
    });
}

module.exports = { runTests };
