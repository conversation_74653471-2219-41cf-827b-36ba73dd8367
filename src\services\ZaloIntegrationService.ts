import { supabaseAdmin } from '../config/supabase';
import { logger } from '../utils/logger';
import { zaloAuthService } from './ZaloAuthService';
import { zaloSessionManager } from './ZaloSessionManager';
import * as crypto from 'crypto';

// Declare require for Node.js
declare const require: any;
const { Zalo } = require('zca-js');

export interface ZaloLoginResult {
  success: boolean;
  accountId?: string;
  zaloUserId?: string;
  displayName?: string;
  error?: string;
}

export class ZaloIntegrationService {
  private defaultTenantId: string | null = null;
  private defaultInboxId: number | null = null;

  constructor() {
    // Set default values from environment or config
    this.defaultTenantId = process.env.DEFAULT_TENANT_ID || null;
    this.defaultInboxId = process.env.DEFAULT_CHATWOOT_INBOX_ID ? parseInt(process.env.DEFAULT_CHATWOOT_INBOX_ID) : null;
  }

  /**
   * Migrate existing Zalo login to database
   */
  async migrateExistingLogin(
    api: any, 
    tenantId?: string, 
    chatwootInboxId?: number
  ): Promise<ZaloLoginResult> {
    try {
      logger.info('Migrating existing Zalo login to database');

      // Use default values if not provided
      const finalTenantId = tenantId || this.defaultTenantId;
      const finalInboxId = chatwootInboxId || this.defaultInboxId;

      if (!finalTenantId) {
        throw new Error('Tenant ID is required for migration');
      }

      // Get user info from existing API
      const userInfo = await api.fetchAccountInfo();
      if (!userInfo || !userInfo.userId) {
        throw new Error('Failed to get user info from existing login');
      }

      // Get cookies from existing API
      const cookies = await api.getCookie();
      if (!cookies) {
        throw new Error('Failed to get cookies from existing login');
      }

      logger.info('Retrieved user info for migration', {
        userId: userInfo.userId,
        displayName: userInfo.displayName || userInfo.zaloName
      });

      // Check if account already exists
      const { data: existingAccount } = await supabaseAdmin
        .from('zalo_accounts')
        .select('*')
        .eq('tenant_id', finalTenantId)
        .eq('zalo_user_id', userInfo.userId)
        .single();

      let accountId: string;

      if (existingAccount) {
        // Update existing account
        logger.info('Updating existing account', { accountId: existingAccount.id });
        
        const { data: updatedAccount, error } = await supabaseAdmin
          .from('zalo_accounts')
          .update({
            auth_data: {
              cookies: cookies,
              imei: crypto.randomUUID(),
              user_agent: userInfo.userAgent || 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
              session_token: crypto.randomUUID()
            },
            status: 'active',
            zalo_display_name: userInfo.displayName || userInfo.zaloName,
            zalo_phone: userInfo.phoneNumber,
            zalo_avatar_url: userInfo.avatar,
            chatwoot_inbox_id: finalInboxId,
            chatwoot_source_id: `zalo_${existingAccount.id}`,
            chatwoot_channel_status: finalInboxId ? 'active' : 'inactive',
            last_login_at: new Date().toISOString(),
            last_activity_at: new Date().toISOString(),
            expires_at: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString(), // 30 days
            login_method: 'cookie',
            error_data: {} // Clear any previous errors
          })
          .eq('id', existingAccount.id)
          .select()
          .single();

        if (error) {
          throw new Error(`Failed to update existing account: ${error.message}`);
        }

        accountId = updatedAccount.id;
      } else {
        // Create new account
        logger.info('Creating new account for migration');
        
        const { data: newAccount, error } = await supabaseAdmin
          .from('zalo_accounts')
          .insert({
            tenant_id: finalTenantId,
            zalo_user_id: userInfo.userId,
            zalo_display_name: userInfo.displayName || userInfo.zaloName,
            zalo_phone: userInfo.phoneNumber,
            zalo_avatar_url: userInfo.avatar,
            auth_data: {
              cookies: cookies,
              imei: crypto.randomUUID(),
              user_agent: userInfo.userAgent || 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
              session_token: crypto.randomUUID()
            },
            status: 'active',
            chatwoot_inbox_id: finalInboxId,
            chatwoot_source_id: finalInboxId ? `zalo_${crypto.randomUUID()}` : null,
            chatwoot_channel_status: finalInboxId ? 'active' : 'inactive',
            login_method: 'cookie',
            last_login_at: new Date().toISOString(),
            last_activity_at: new Date().toISOString(),
            expires_at: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString()
          })
          .select()
          .single();

        if (error) {
          throw new Error(`Failed to create new account: ${error.message}`);
        }

        accountId = newAccount.id;
      }

      // Register the API instance with ZaloAuthService
      const zaloInstance = zaloAuthService.getZaloInstance(accountId);
      if (!zaloInstance) {
        // Store the existing API instance
        (zaloAuthService as any).zaloInstances.set(accountId, api);
      }

      logger.info('Migration completed successfully', {
        accountId,
        zaloUserId: userInfo.userId,
        displayName: userInfo.displayName || userInfo.zaloName,
        tenantId: finalTenantId,
        chatwootInboxId: finalInboxId
      });

      return {
        success: true,
        accountId,
        zaloUserId: userInfo.userId,
        displayName: userInfo.displayName || userInfo.zaloName
      };

    } catch (error: any) {
      logger.error('Failed to migrate existing login', { error: error.message });
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Create QR login for specific tenant and inbox
   */
  async createQRLogin(tenantId: string, chatwootInboxId?: number): Promise<{
    success: boolean;
    accountId?: string;
    qrCodeUrl?: string;
    error?: string;
  }> {
    try {
      logger.info('Creating QR login', { tenantId, chatwootInboxId });

      const result = await zaloAuthService.generateQRLogin({
        tenant_id: tenantId,
        expires_in_minutes: 10
      });

      // Update account with Chatwoot info if provided
      if (chatwootInboxId) {
        await supabaseAdmin
          .from('zalo_accounts')
          .update({
            chatwoot_inbox_id: chatwootInboxId,
            chatwoot_source_id: `zalo_${result.account_id}`,
            chatwoot_channel_status: 'inactive' // Will be active after successful login
          })
          .eq('id', result.account_id);
      }

      return {
        success: true,
        accountId: result.account_id,
        qrCodeUrl: result.qr_code_url || `/api/zalo-auth/qr-image/${result.qr_session_id}`
      };

    } catch (error: any) {
      logger.error('Failed to create QR login', { error: error.message });
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Load all active accounts from database and initialize them
   */
  async loadAccountsFromDatabase(): Promise<{
    success: boolean;
    loadedCount: number;
    errors: string[];
  }> {
    try {
      logger.info('Loading Zalo accounts from database');

      const { data: accounts, error } = await supabaseAdmin
        .from('zalo_accounts')
        .select('*')
        .eq('status', 'active');

      if (error) {
        throw new Error(`Failed to load accounts: ${error.message}`);
      }

      const errors: string[] = [];
      let loadedCount = 0;

      for (const account of accounts || []) {
        try {
          logger.info('Initializing account from database', {
            accountId: account.id,
            zaloUserId: account.zalo_user_id,
            displayName: account.zalo_display_name
          });

          // Check if already initialized
          const existingInstance = zaloAuthService.getZaloInstance(account.id);
          if (existingInstance) {
            logger.info('Account already initialized, skipping', { accountId: account.id });
            loadedCount++;
            continue;
          }

          // Initialize Zalo instance
          const zalo = new Zalo({
            selfListen: false,
            checkUpdate: false,
            logging: false
          });

          const api = await zalo.login({
            cookie: account.auth_data.cookies,
            imei: account.auth_data.imei,
            userAgent: account.auth_data.user_agent
          });

          // Test connection
          const userInfo = await api.fetchAccountInfo();
          if (!userInfo || !userInfo.userId) {
            throw new Error('Failed to validate account after initialization');
          }

          // Start listener
          api.listener.start();

          // Store instance
          (zaloAuthService as any).zaloInstances.set(account.id, api);

          // Update last activity
          await supabaseAdmin
            .from('zalo_accounts')
            .update({ 
              last_activity_at: new Date().toISOString(),
              status: 'active'
            })
            .eq('id', account.id);

          loadedCount++;
          logger.info('Account initialized successfully', {
            accountId: account.id,
            zaloUserId: userInfo.userId
          });

        } catch (error: any) {
          const errorMsg = `Failed to initialize account ${account.id}: ${error.message}`;
          errors.push(errorMsg);
          logger.error(errorMsg);

          // Update account status to error
          await supabaseAdmin
            .from('zalo_accounts')
            .update({
              status: 'error',
              error_data: {
                error_message: error.message,
                error_code: 'INIT_FAILED',
                retry_count: 0,
                next_retry_at: new Date(Date.now() + 30 * 60 * 1000).toISOString()
              }
            })
            .eq('id', account.id);
        }
      }

      logger.info('Database account loading completed', {
        totalAccounts: accounts?.length || 0,
        loadedCount,
        errorCount: errors.length
      });

      return {
        success: true,
        loadedCount,
        errors
      };

    } catch (error: any) {
      logger.error('Failed to load accounts from database', { error: error.message });
      return {
        success: false,
        loadedCount: 0,
        errors: [error.message]
      };
    }
  }

  /**
   * Set default tenant and inbox for migrations
   */
  setDefaults(tenantId: string, chatwootInboxId?: number): void {
    this.defaultTenantId = tenantId;
    this.defaultInboxId = chatwootInboxId;
    logger.info('Default values updated', { tenantId, chatwootInboxId });
  }

  /**
   * Get current defaults
   */
  getDefaults(): { tenantId: string | null; inboxId: number | null } {
    return {
      tenantId: this.defaultTenantId,
      inboxId: this.defaultInboxId
    };
  }
}

// Export singleton instance
export const zaloIntegrationService = new ZaloIntegrationService();
