# Hệ thống QR Code Authentication cho Zalo Integration

## Tổng quan

Hệ thống QR Code Authentication cho phép người dùng đăng nhập vào Zalo thông qua việc quét mã QR, sau đó tự động tích hợp với Chatwoot để xử lý tin nhắn.

## Kiến trúc hệ thống

### 1. Database Schema

#### Bảng `qr_codes`
```sql
- id: UUID (Primary Key)
- tenant_id: UUID (Foreign Key to tenants)
- session_id: VARCHAR(255) (Unique)
- account_id: UUID (Foreign Key to zalo_accounts)
- qr_image_url: TEXT (URL của QR code trên Supabase Storage)
- qr_local_path: TEXT (Đường dẫn local backup)
- status: VARCHAR(50) (pending, scanned, confirmed, expired, failed)
- expires_at: TIMESTAMPTZ
- scanned_at: TIMESTAMPTZ
- confirmed_at: TIMESTAMPTZ
- chatwoot_inbox_id: INTEGER
- chatwoot_source_id: VARCHAR(255)
- user_agent: TEXT
- client_ip: INET
- metadata: JSONB
- created_at: TIMESTAMPTZ
- updated_at: TIMESTAMPTZ
```

#### Bảng `zalo_accounts` (đã có)
- Lưu trữ thông tin tài khoản Zalo sau khi đăng nhập thành công
- Bao gồm auth_data, qr_data, chatwoot integration info

### 2. Supabase Storage
- Bucket: `qr-codes`
- Public access cho việc hiển thị QR codes
- Tự động cleanup khi QR expires

## API Endpoints

### 1. QR Management API (`/api/qr-management`)

#### POST `/api/qr-management/create`
Tạo QR code mới với tích hợp Chatwoot

**Headers:**
```
X-Tenant-ID: <tenant-uuid>
Content-Type: application/json
```

**Body:**
```json
{
  "chatwoot_inbox_id": 281,
  "expires_in_minutes": 10,
  "user_agent": "MyApp/1.0"
}
```

**Response:**
```json
{
  "success": true,
  "message": "QR code created successfully",
  "data": {
    "account_id": "uuid",
    "session_id": "uuid",
    "qr_code_url": "https://supabase-url/storage/v1/object/public/qr-codes/session-id.png",
    "expires_at": "2025-01-22T03:00:00.000Z",
    "status": "pending",
    "chatwoot_inbox_id": 281,
    "instructions": {
      "step1": "Display the QR code to user using qr_code_url",
      "step2": "User scans QR code with Zalo app",
      "step3": "Poll QR status using session_id",
      "step4": "Account will be automatically activated when login completes"
    }
  }
}
```

#### GET `/api/qr-management/status/:sessionId`
Kiểm tra trạng thái QR code

**Response:**
```json
{
  "success": true,
  "data": {
    "session_id": "uuid",
    "status": "pending|scanned|confirmed|expired|failed",
    "qr_image_url": "https://...",
    "expires_at": "2025-01-22T03:00:00.000Z",
    "scanned_at": null,
    "confirmed_at": null,
    "account_status": {
      "account_id": "uuid",
      "zalo_user_id": "*********",
      "status": "active",
      "is_connected": true
    }
  }
}
```

#### GET `/api/qr-management/list`
Lấy danh sách QR codes

**Query Parameters:**
- `status`: Filter theo trạng thái
- `limit`: Số lượng records (default: 20)
- `offset`: Offset cho pagination (default: 0)

#### DELETE `/api/qr-management/:sessionId`
Xóa QR code và cleanup storage

### 2. Integration API (`/api/zalo-integration`)

#### POST `/api/zalo-integration/create-qr`
Tạo QR code với tích hợp Chatwoot (legacy endpoint)

**Body:**
```json
{
  "chatwoot_inbox_id": 281,
  "expires_in_minutes": 10
}
```

## Frontend Integration

### 1. Tạo và hiển thị QR Code

```javascript
// Tạo QR code
const createQRCode = async (tenantId, inboxId) => {
  const response = await fetch('/api/qr-management/create', {
    method: 'POST',
    headers: {
      'X-Tenant-ID': tenantId,
      'Content-Type': 'application/json'
    },
    body: JSON.stringify({
      chatwoot_inbox_id: inboxId,
      expires_in_minutes: 10
    })
  });
  
  const result = await response.json();
  return result.data;
};

// Hiển thị QR code
const displayQRCode = (qrData) => {
  const img = document.createElement('img');
  img.src = qrData.qr_code_url;
  img.alt = 'Zalo QR Code';
  document.getElementById('qr-container').appendChild(img);
  
  // Start polling for status
  pollQRStatus(qrData.session_id);
};
```

### 2. Polling trạng thái QR

```javascript
const pollQRStatus = async (sessionId) => {
  const poll = async () => {
    try {
      const response = await fetch(`/api/qr-management/status/${sessionId}`, {
        headers: {
          'X-Tenant-ID': tenantId
        }
      });
      
      const result = await response.json();
      const status = result.data.status;
      
      switch (status) {
        case 'pending':
          // Continue polling
          setTimeout(poll, 2000);
          break;
        case 'scanned':
          showMessage('QR code đã được quét, đang chờ xác nhận...');
          setTimeout(poll, 1000);
          break;
        case 'confirmed':
          showMessage('Đăng nhập thành công!');
          onLoginSuccess(result.data.account_status);
          break;
        case 'expired':
          showMessage('QR code đã hết hạn, vui lòng tạo mới');
          break;
        case 'failed':
          showMessage('Đăng nhập thất bại, vui lòng thử lại');
          break;
      }
    } catch (error) {
      console.error('Polling error:', error);
      setTimeout(poll, 5000); // Retry after 5s
    }
  };
  
  poll();
};
```

## Testing

### 1. Chạy test tự động
```bash
yarn test-qr
```

### 2. Test thủ công

1. **Tạo QR Code:**
```bash
curl -X POST http://localhost:3355/api/qr-management/create \
  -H "X-Tenant-ID: 9e20b074-5c04-4241-b2bd-58c0499c1f86" \
  -H "Content-Type: application/json" \
  -d '{"chatwoot_inbox_id": 281, "expires_in_minutes": 10}'
```

2. **Kiểm tra trạng thái:**
```bash
curl -X GET http://localhost:3355/api/qr-management/status/{session-id} \
  -H "X-Tenant-ID: 9e20b074-5c04-4241-b2bd-58c0499c1f86"
```

3. **Lấy danh sách QR codes:**
```bash
curl -X GET http://localhost:3355/api/qr-management/list \
  -H "X-Tenant-ID: 9e20b074-5c04-4241-b2bd-58c0499c1f86"
```

## Troubleshooting

### 1. Lỗi "Unable to login with QRCode"
- Kiểm tra zca-js version
- Đảm bảo thư mục qr_codes có quyền write
- Kiểm tra network connectivity

### 2. QR code không hiển thị
- Kiểm tra Supabase Storage configuration
- Verify bucket permissions
- Check public URL generation

### 3. Polling không hoạt động
- Kiểm tra CORS settings
- Verify tenant ID header
- Check session expiration

## Configuration

### Environment Variables
```env
SUPABASE_URL=https://your-project.supabase.co
SUPABASE_SERVICE_KEY=your-service-key
CHATWOOT_BASE_URL=https://app.mooly.vn
CHATWOOT_API_ACCESS_TOKEN=your-token
CHATWOOT_ACCOUNT_ID=213
CHATWOOT_INBOX_ID=281
```

### Supabase Setup
1. Create bucket `qr-codes` with public access
2. Set up RLS policies for tenant isolation
3. Configure storage policies for public read access

## Security Considerations

1. **Tenant Isolation:** Tất cả operations đều được filter theo tenant_id
2. **QR Expiration:** QR codes tự động expire sau thời gian định trước
3. **Storage Cleanup:** Tự động xóa QR images khi expired
4. **Rate Limiting:** Giới hạn số lượng QR codes có thể tạo
5. **Session Management:** Secure session handling với proper cleanup

## Monitoring

- Logs được ghi chi tiết cho tất cả operations
- Health check endpoints để monitor system status
- Metrics tracking cho QR creation/completion rates
- Error tracking và alerting
