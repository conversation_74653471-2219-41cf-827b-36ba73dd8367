import { supabaseAdmin } from '../config/supabase';
import { logger } from '../utils/logger';
import { Zalo } from 'zca-js';
import * as fs from 'fs';
import * as path from 'path';
import * as crypto from 'crypto';

// Interfaces
export interface ZaloAccount {
  id: string;
  tenant_id: string;
  zalo_user_id: string;
  zalo_display_name?: string;
  zalo_avatar_url?: string;
  status: string;
  auth_data: {
    user_agent: string;
    imei: string;
    cookies: any;
  };
  qr_data?: any;
  login_method: string;
  last_activity_at?: string;
  error_data?: any;
  created_at?: string;
  updated_at?: string;
}

export interface QRLoginRequest {
  tenant_id: string;
  user_agent?: string;
  expires_in_minutes?: number;
}

export interface QRLoginResponse {
  account_id: string;
  qr_code_path: string;
  qr_code_url?: string;
  qr_session_id: string;
  expires_at: string;
  status: string;
}

export interface CookieLoginRequest {
  tenant_id: string;
  cookies: any;
  user_agent?: string;
  imei?: string;
}

export interface AccountStatusResponse {
  account_id: string;
  zalo_user_id?: string;
  status: string;
  is_connected: boolean;
  last_activity_at?: string;
  error_message?: string;
}

export class ZaloAuthService {
  private zaloInstances: Map<string, any> = new Map(); // accountId -> Zalo instance
  private qrSessions: Map<string, any> = new Map(); // sessionId -> session data

  constructor() {
    this.initializeFromDatabase();
  }

  /**
   * Initialize from database
   */
  private async initializeFromDatabase(): Promise<void> {
    try {
      logger.info('Loading Zalo accounts from database');

      const { data: accounts, error } = await supabaseAdmin
        .from('zalo_accounts')
        .select('*')
        .eq('status', 'active')
        .not('auth_data->cookies', 'is', null);

      if (error) {
        logger.error('Failed to load accounts from database', { error: error.message });
        return;
      }

      let loadedCount = 0;
      let errorCount = 0;

      for (const account of accounts || []) {
        try {
          await this.initializeAccountFromData(account);
          loadedCount++;
        } catch (error: any) {
          logger.error('Failed to initialize account', {
            accountId: account.id,
            error: error.message
          });
          errorCount++;
        }
      }

      logger.info('Database account loading completed', {
        totalAccounts: accounts?.length || 0,
        loadedCount,
        errorCount
      });

    } catch (error: any) {
      logger.error('Failed to initialize from database', { error: error.message });
    }
  }

  /**
   * Initialize account from database data
   */
  private async initializeAccountFromData(account: any): Promise<void> {
    try {
      if (!account.auth_data?.cookies) {
        logger.warn('Account has no cookies, skipping', { accountId: account.id });
        return;
      }

      const zalo = new Zalo({
        selfListen: false,
        checkUpdate: false
      });

      // Test connection
      const api = await zalo.login({
        cookie: account.auth_data.cookies,
        userAgent: account.auth_data.user_agent,
        imei: account.auth_data.imei || ''
      });
      
      // Store instance
      this.zaloInstances.set(account.id, api);

      // Start listener
      api.listener.start();

      logger.info('Account initialized successfully', {
        accountId: account.id,
        zaloUserId: account.zalo_user_id
      });

    } catch (error: any) {
      logger.error('Failed to initialize account from data', {
        accountId: account.id,
        error: error.message
      });
      throw error;
    }
  }

  /**
   * Generate QR code for login
   */
  async generateQRLogin(request: QRLoginRequest): Promise<QRLoginResponse> {
    try {
      logger.info('Generating QR login', { tenantId: request.tenant_id });

      const qrSessionId = crypto.randomUUID();
      const expiresAt = new Date(Date.now() + (request.expires_in_minutes || 10) * 60 * 1000);
      const qrPath = path.join(process.cwd(), 'qr_codes', `${qrSessionId}.png`);

      // Ensure qr_codes directory exists
      const qrDir = path.dirname(qrPath);
      if (!fs.existsSync(qrDir)) {
        fs.mkdirSync(qrDir, { recursive: true });
      }

      // Create temporary account record
      const { data: account, error } = await supabaseAdmin
        .from('zalo_accounts')
        .insert({
          tenant_id: request.tenant_id,
          zalo_user_id: 'pending_' + qrSessionId,
          status: 'qr_pending',
          auth_data: {
            user_agent: request.user_agent || 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
            imei: '',
            cookies: {}
          },
          qr_data: {
            qr_session_id: qrSessionId,
            qr_status: 'pending',
            qr_expires_at: expiresAt.toISOString(),
            qr_code_path: qrPath
          },
          login_method: 'qr'
        })
        .select()
        .single();

      if (error) {
        throw new Error(`Failed to create QR session: ${error.message}`);
      }

      // Create Zalo instance for QR login
      const zalo = new Zalo({
        selfListen: false,
        checkUpdate: false,
        logging: true
      });

      try {
        // Generate QR code with proper callback handling
        logger.info('Starting zca-js QR generation', { sessionId: qrSessionId });
        
        const loginPromise = zalo.loginQR({
          userAgent: request.user_agent || 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
          qrPath: qrPath
        }, async (event: any) => {
          // Handle QR events according to zca-js documentation
          logger.info('QR Event received', { 
            type: event.type, 
            sessionId: qrSessionId
          });
          
          if (event.type === 'QRCodeGenerated') {
            // QR code has been generated and saved
            logger.info('QR code generated successfully', { qrPath, sessionId: qrSessionId });
            
            // Save QR to file using the provided action
            if (event.actions?.saveToFile) {
              await event.actions.saveToFile(qrPath);
            }
            
          } else if (event.type === 'QRCodeExpired') {
            logger.warn('QR code expired', { sessionId: qrSessionId });
            
            // Update QR status
            await supabaseAdmin
              .from('qr_codes')
              .update({ status: 'expired' })
              .eq('session_id', qrSessionId);
              
          } else if (event.type === 'Error') {
            logger.error('QR generation error', { 
              sessionId: qrSessionId, 
              error: event.error || event.message 
            });
          }
        });

        // Handle login completion asynchronously
        this.handleQRLoginCompletion(loginPromise, qrSessionId, account.id, request.tenant_id);

        // Wait a bit for QR generation
        await new Promise(resolve => setTimeout(resolve, 3000));

        // Check if QR file was created
        if (!fs.existsSync(qrPath)) {
          throw new Error('QR code file was not generated');
        }

        // Upload QR code to Supabase Storage
        const qrImageUrl = await this.uploadQRToStorage(qrPath, qrSessionId);

        // Create QR code record in database
        const { data: qrRecord, error: qrError } = await supabaseAdmin
          .from('qr_codes')
          .insert({
            tenant_id: request.tenant_id,
            session_id: qrSessionId,
            account_id: account.id,
            qr_image_url: qrImageUrl,
            qr_local_path: qrPath,
            status: 'pending',
            expires_at: expiresAt.toISOString(),
            user_agent: request.user_agent,
            metadata: {
              generated_at: new Date().toISOString()
            }
          })
          .select()
          .single();

        if (qrError) {
          logger.error('Failed to create QR record', { error: qrError.message });
        }

        // Store QR session
        this.qrSessions.set(qrSessionId, {
          accountId: account.id,
          tenantId: request.tenant_id,
          expiresAt: expiresAt,
          qrImageUrl: qrImageUrl
        });

        logger.info('QR login session created successfully', {
          accountId: account.id,
          sessionId: qrSessionId,
          expiresAt: expiresAt.toISOString(),
          qrImageUrl: qrImageUrl
        });

        return {
          account_id: account.id,
          qr_code_path: qrPath,
          qr_code_url: qrImageUrl,
          qr_session_id: qrSessionId,
          expires_at: expiresAt.toISOString(),
          status: 'pending'
        };

      } catch (qrError: any) {
        logger.error('QR generation failed', {
          sessionId: qrSessionId,
          error: qrError.message
        });

        // Update account status to error
        await supabaseAdmin
          .from('zalo_accounts')
          .update({
            status: 'error',
            error_data: {
              error_message: qrError.message,
              error_code: 'QR_GENERATION_FAILED'
            }
          })
          .eq('id', account.id);

        throw new Error(`Unable to login with QRCode: ${qrError.message}`);
      }

    } catch (error: any) {
      logger.error('Failed to generate QR login', {
        tenantId: request.tenant_id,
        error: error.message
      });
      throw error;
    }
  }

  /**
   * Upload QR code image to Supabase Storage
   */
  private async uploadQRToStorage(qrPath: string, sessionId: string): Promise<string> {
    try {
      const qrBuffer = fs.readFileSync(qrPath);
      const storagePath = `qr-codes/${sessionId}.png`;
      
      const { data, error } = await supabaseAdmin.storage
        .from('qr-codes')
        .upload(storagePath, qrBuffer, {
          contentType: 'image/png',
          cacheControl: '3600',
          upsert: true
        });

      if (error) {
        throw new Error(`Failed to upload QR to storage: ${error.message}`);
      }

      const { data: publicUrlData } = supabaseAdmin.storage
        .from('qr-codes')
        .getPublicUrl(storagePath);

      return publicUrlData.publicUrl;

    } catch (error: any) {
      logger.error('Failed to upload QR to storage', {
        sessionId,
        error: error.message
      });
      throw error;
    }
  }

  /**
   * Handle QR login completion asynchronously
   */
  private async handleQRLoginCompletion(loginPromise: Promise<any>, sessionId: string, accountId: string, tenantId: string): Promise<void> {
    try {
      logger.info('Waiting for QR login completion', { sessionId });
      
      const api = await loginPromise;
      
      logger.info('QR login completed successfully', { sessionId });
      
      // Get user info and cookies
      const userInfo = await api.fetchAccountInfo();
      const cookies = await api.getCookie();
      
      // Update account with real data
      await supabaseAdmin
        .from('zalo_accounts')
        .update({
          zalo_user_id: userInfo.uid,
          zalo_display_name: userInfo.name,
          zalo_avatar_url: userInfo.avatar,
          status: 'active',
          auth_data: {
            user_agent: this.qrSessions.get(sessionId)?.userAgent || '',
            imei: userInfo.imei || '',
            cookies: cookies
          },
          last_activity_at: new Date().toISOString()
        })
        .eq('id', accountId);

      // Update QR code status
      await supabaseAdmin
        .from('qr_codes')
        .update({
          status: 'confirmed',
          confirmed_at: new Date().toISOString()
        })
        .eq('session_id', sessionId);

      // Store API instance
      this.zaloInstances.set(accountId, api);
      
      // Start listener
      api.listener.start();
      
      logger.info('QR login process completed', {
        sessionId,
        accountId,
        zaloUserId: userInfo.uid
      });

    } catch (error: any) {
      logger.error('QR login completion failed', {
        sessionId,
        accountId,
        error: error.message
      });

      await supabaseAdmin
        .from('zalo_accounts')
        .update({ status: 'error' })
        .eq('id', accountId);

      await supabaseAdmin
        .from('qr_codes')
        .update({ status: 'failed' })
        .eq('session_id', sessionId);

      this.qrSessions.delete(sessionId);
    }
  }

  /**
   * Get account status
   */
  async getAccountStatus(accountId: string, tenantId: string): Promise<AccountStatusResponse> {
    try {
      const { data: account, error } = await supabaseAdmin
        .from('zalo_accounts')
        .select('*')
        .eq('id', accountId)
        .eq('tenant_id', tenantId)
        .single();

      if (error || !account) {
        throw new Error('Account not found');
      }

      const zaloInstance = this.zaloInstances.get(accountId);
      const isConnected = !!zaloInstance;

      return {
        account_id: accountId,
        zalo_user_id: account.zalo_user_id,
        status: account.status,
        is_connected: isConnected,
        last_activity_at: account.last_activity_at,
        error_message: account.error_data?.error_message
      };

    } catch (error: any) {
      logger.error('Failed to get account status', {
        accountId,
        error: error.message
      });
      throw error;
    }
  }

  /**
   * Get Zalo instance by account ID
   */
  getZaloInstance(accountId: string): any | null {
    return this.zaloInstances.get(accountId) || null;
  }
}

// Export singleton instance
export const zaloAuthService = new ZaloAuthService();
