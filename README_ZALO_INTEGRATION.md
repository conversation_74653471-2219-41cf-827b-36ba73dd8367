# 🚀 Zalo Multi-Account Integration - Complete System

## 📋 Tổng quan

Hệ thống Zalo Multi-Account Authentication đã được triển khai hoàn chỉnh với khả năng:

- ✅ **Auto-migrate** existing Zalo login vào database
- ✅ **Multi-tenant support** với RLS security  
- ✅ **QR Code authentication** cho accounts mới
- ✅ **Cookie-based login** cho existing accounts
- ✅ **Auto-recovery** từ database khi restart
- ✅ **Chatwoot integration** seamless
- ✅ **Real-time monitoring** và health checks
- ✅ **Rate limiting** và security middleware

## 🔧 Quick Setup

### 1. Environment Variables

Thêm vào `.env`:

```env
# Required for integration
DEFAULT_TENANT_ID=your-tenant-uuid-here
DEFAULT_CHATWOOT_INBOX_ID=123

# Optional monitoring
MONITORING_WEBHOOK_URL=your-webhook-url
SLACK_WEBHOOK_URL=your-slack-webhook
```

### 2. Verify Database

Kiểm tra bảng `zalo_accounts` đã được tạo:

```sql
SELECT * FROM zalo_accounts LIMIT 1;
```

### 3. Start Server

```bash
npm start
```

Server sẽ tự động:
- Load existing accounts từ database
- Migrate current Zalo login (nếu có)
- Start monitoring services

## 🎯 Sử dụng ngay

### Scenario 1: Bạn đã có Zalo login (như hiện tại)

Khi server start, login hiện tại sẽ **tự động được lưu vào database**:

```
[INFO] Migrating Zalo login to database...
[INFO] Successfully migrated login to database
```

Kiểm tra:
```bash
curl -H "X-Tenant-ID: your-tenant-uuid" \
  http://localhost:3355/api/zalo-integration/status
```

### Scenario 2: Tạo QR login mới cho tenant/inbox khác

```bash
curl -X POST http://localhost:3355/api/zalo-integration/create-qr \
  -H "X-Tenant-ID: your-tenant-uuid" \
  -H "Content-Type: application/json" \
  -d '{
    "chatwoot_inbox_id": 456,
    "expires_in_minutes": 10
  }'
```

### Scenario 3: Server restart - Auto recovery

Khi restart server, tất cả accounts sẽ được **tự động load từ database**:

```
[INFO] Loading existing accounts from database
[INFO] Account initialized successfully
```

## 📡 API Endpoints

### 🔐 Integration Management

```
POST   /api/zalo-integration/create-qr        # Tạo QR với tenant + inbox
GET    /api/zalo-integration/status           # Status của tenant
POST   /api/zalo-integration/set-defaults     # Set default values
POST   /api/zalo-integration/reload-accounts  # Reload từ database
GET    /api/zalo-integration/health           # Health check
```

### 👤 Account Management

```
GET    /api/zalo-auth/accounts                # List accounts
GET    /api/zalo-auth/account/:id             # Account details  
POST   /api/zalo-auth/refresh/:id             # Refresh session
DELETE /api/zalo-auth/account/:id             # Remove account
GET    /api/zalo-auth/qr-status/:id           # Check QR status
```

### 📊 Monitoring

```
GET    /api/zalo-monitoring/dashboard         # Dashboard data
GET    /api/zalo-monitoring/metrics           # System metrics
GET    /api/zalo-monitoring/accounts          # Account metrics
GET    /api/zalo-monitoring/health            # System health
```

## 🧪 Testing

Chạy test suite để verify hệ thống:

```bash
# Install axios if not installed
npm install axios

# Set test environment
export TEST_TENANT_ID=your-tenant-uuid
export TEST_INBOX_ID=123

# Run tests
node scripts/test-integration.js
```

Test sẽ kiểm tra:
- ✅ Health checks
- ✅ Integration status  
- ✅ QR login creation
- ✅ Account management
- ✅ Monitoring dashboard
- ✅ Auto-recovery

## 🔍 Monitoring Dashboard

### Key Metrics

```bash
# System overview
curl -H "X-Tenant-ID: your-tenant-uuid" \
  http://localhost:3355/api/zalo-monitoring/dashboard
```

Response:
```json
{
  "overview": {
    "total_accounts": 2,
    "active_accounts": 2,
    "healthy_connections": 2,
    "error_accounts": 0
  },
  "performance": {
    "averageUptime": 98.5,
    "averageResponseTime": 150,
    "recentActivity": 2,
    "criticalAccounts": 0
  }
}
```

### Health Status

```bash
curl http://localhost:3355/api/zalo-monitoring/health
```

## 🚨 Troubleshooting

### Issue: Migration không hoạt động

**Symptoms**: Existing login không được lưu vào database

**Solution**:
1. Check `DEFAULT_TENANT_ID` trong `.env`
2. Verify database connection
3. Check logs cho migration errors

### Issue: Accounts không load sau restart

**Symptoms**: Server restart nhưng không load accounts

**Solution**:
1. Check database có data không: `SELECT * FROM zalo_accounts;`
2. Verify account status = 'active'
3. Check auth_data có cookies không
4. Run reload manually: `POST /api/zalo-integration/reload-accounts`

### Issue: QR Login không hoạt động

**Symptoms**: QR tạo được nhưng không login được

**Solution**:
1. Check QR expiry time
2. Verify tenant permissions
3. Check rate limiting (10 QR/15min per tenant)
4. Verify zca-js compatibility

## 🎯 Production Checklist

### Pre-deployment

- [ ] Environment variables configured
- [ ] Database schema deployed  
- [ ] Default tenant/inbox set
- [ ] Health checks passing
- [ ] Rate limiting configured

### Post-deployment

- [ ] Existing logins migrated
- [ ] New QR logins working
- [ ] Auto-recovery after restarts
- [ ] Chatwoot integration functional
- [ ] Monitoring alerts active

## 📈 Usage Examples

### Frontend Integration

```javascript
// Tạo QR login
const createZaloLogin = async (tenantId, inboxId) => {
  const response = await fetch('/api/zalo-integration/create-qr', {
    method: 'POST',
    headers: {
      'X-Tenant-ID': tenantId,
      'Content-Type': 'application/json'
    },
    body: JSON.stringify({
      chatwoot_inbox_id: inboxId,
      expires_in_minutes: 10
    })
  });
  
  const result = await response.json();
  return result.data;
};

// Poll QR status
const pollQRStatus = async (accountId, tenantId) => {
  const poll = setInterval(async () => {
    const response = await fetch(`/api/zalo-auth/qr-status/${accountId}`, {
      headers: { 'X-Tenant-ID': tenantId }
    });
    
    const result = await response.json();
    
    if (result.data.status === 'confirmed') {
      clearInterval(poll);
      console.log('Login successful!');
      // Account is now active and linked to Chatwoot
    }
  }, 3000);
};

// Get account list
const getAccounts = async (tenantId) => {
  const response = await fetch('/api/zalo-auth/accounts', {
    headers: { 'X-Tenant-ID': tenantId }
  });
  
  return response.json();
};
```

### Backend Integration

```javascript
// Check if tenant has active Zalo accounts
const hasActiveZaloAccounts = async (tenantId) => {
  const response = await fetch(`/api/zalo-integration/status`, {
    headers: { 'X-Tenant-ID': tenantId }
  });
  
  const result = await response.json();
  return result.data.active_accounts > 0;
};

// Monitor account health
const monitorAccountHealth = async (tenantId) => {
  const response = await fetch(`/api/zalo-monitoring/accounts`, {
    headers: { 'X-Tenant-ID': tenantId }
  });
  
  const result = await response.json();
  const unhealthyAccounts = result.data.accounts.filter(acc => !acc.is_healthy);
  
  if (unhealthyAccounts.length > 0) {
    console.warn('Unhealthy accounts detected:', unhealthyAccounts);
  }
};
```

## 🎉 Kết luận

Hệ thống đã hoàn chỉnh và sẵn sàng production với:

- **Automatic migration** của existing logins
- **Multi-tenant architecture** secure và scalable
- **Real-time monitoring** và health checks
- **Auto-recovery** mechanisms
- **Comprehensive APIs** cho frontend integration
- **Production-ready** với proper error handling

**System is ready for production deployment! 🚀**

---

## 📞 Support

Nếu có vấn đề, check:
1. **Logs**: Server logs cho detailed errors
2. **Health endpoints**: `/api/zalo-integration/health`
3. **Database**: Verify data trong `zalo_accounts` table
4. **Test script**: `node scripts/test-integration.js`
