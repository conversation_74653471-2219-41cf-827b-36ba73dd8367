import { supabaseAdmin } from '../config/supabase';
import { logger } from '../utils/logger';
import * as fs from 'fs';

export class QRCleanupService {
  private cleanupInterval: NodeJS.Timeout | null = null;
  private isRunning = false;

  /**
   * Start the cleanup service
   */
  start(): void {
    if (this.isRunning) {
      logger.warn('QR Cleanup service is already running');
      return;
    }

    this.isRunning = true;
    
    // Run cleanup immediately
    this.performCleanup();
    
    // Schedule cleanup every 5 minutes
    this.cleanupInterval = setInterval(() => {
      this.performCleanup();
    }, 5 * 60 * 1000);

    logger.info('QR Cleanup service started');
  }

  /**
   * Stop the cleanup service
   */
  stop(): void {
    if (this.cleanupInterval) {
      clearInterval(this.cleanupInterval);
      this.cleanupInterval = null;
    }
    
    this.isRunning = false;
    logger.info('QR Cleanup service stopped');
  }

  /**
   * Perform cleanup of expired QR codes
   */
  private async performCleanup(): Promise<void> {
    try {
      logger.info('Starting QR cleanup process');

      const now = new Date().toISOString();
      
      // Find expired QR codes
      const { data: expiredQRs, error: fetchError } = await supabaseAdmin
        .from('qr_codes')
        .select('*')
        .lt('expires_at', now)
        .in('status', ['pending', 'scanned']);

      if (fetchError) {
        throw new Error(`Failed to fetch expired QR codes: ${fetchError.message}`);
      }

      if (!expiredQRs || expiredQRs.length === 0) {
        logger.info('No expired QR codes found');
        return;
      }

      logger.info(`Found ${expiredQRs.length} expired QR codes to cleanup`);

      // Process each expired QR
      for (const qr of expiredQRs) {
        await this.cleanupSingleQR(qr);
      }

      // Update expired QR codes status
      const { error: updateError } = await supabaseAdmin
        .from('qr_codes')
        .update({ 
          status: 'expired',
          updated_at: now
        })
        .lt('expires_at', now)
        .in('status', ['pending', 'scanned']);

      if (updateError) {
        logger.error('Failed to update expired QR codes status', { error: updateError.message });
      }

      // Cleanup old expired records (older than 24 hours)
      await this.cleanupOldRecords();

      logger.info('QR cleanup process completed successfully');

    } catch (error: any) {
      logger.error('QR cleanup process failed', { error: error.message });
    }
  }

  /**
   * Cleanup a single QR code
   */
  private async cleanupSingleQR(qr: any): Promise<void> {
    try {
      // Delete from Supabase Storage
      if (qr.qr_image_url) {
        try {
          const storagePath = `qr-codes/${qr.session_id}.png`;
          const { error: storageError } = await supabaseAdmin.storage
            .from('qr-codes')
            .remove([storagePath]);

          if (storageError) {
            logger.warn('Failed to delete QR from storage', { 
              sessionId: qr.session_id,
              error: storageError.message 
            });
          } else {
            logger.debug('QR image deleted from storage', { sessionId: qr.session_id });
          }
        } catch (storageError: any) {
          logger.warn('Storage deletion error', { 
            sessionId: qr.session_id,
            error: storageError.message 
          });
        }
      }

      // Delete local file if exists
      if (qr.qr_local_path && fs.existsSync(qr.qr_local_path)) {
        try {
          fs.unlinkSync(qr.qr_local_path);
          logger.debug('Local QR file deleted', { 
            sessionId: qr.session_id,
            path: qr.qr_local_path 
          });
        } catch (fileError: any) {
          logger.warn('Failed to delete local QR file', { 
            sessionId: qr.session_id,
            path: qr.qr_local_path,
            error: fileError.message 
          });
        }
      }

      // Update related zalo_accounts if needed
      if (qr.account_id) {
        try {
          const { error: accountError } = await supabaseAdmin
            .from('zalo_accounts')
            .update({
              qr_data: {
                ...qr.qr_data,
                qr_status: 'expired',
                expired_at: new Date().toISOString()
              }
            })
            .eq('id', qr.account_id)
            .eq('status', 'qr_pending');

          if (accountError) {
            logger.warn('Failed to update related zalo account', { 
              accountId: qr.account_id,
              error: accountError.message 
            });
          }
        } catch (accountError: any) {
          logger.warn('Account update error', { 
            accountId: qr.account_id,
            error: accountError.message 
          });
        }
      }

    } catch (error: any) {
      logger.error('Failed to cleanup single QR', { 
        sessionId: qr.session_id,
        error: error.message 
      });
    }
  }

  /**
   * Cleanup old expired records (older than 24 hours)
   */
  private async cleanupOldRecords(): Promise<void> {
    try {
      const cutoffTime = new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString();
      
      // Get old records first for cleanup
      const { data: oldRecords, error: fetchError } = await supabaseAdmin
        .from('qr_codes')
        .select('*')
        .eq('status', 'expired')
        .lt('updated_at', cutoffTime);

      if (fetchError) {
        logger.warn('Failed to fetch old QR records', { error: fetchError.message });
        return;
      }

      if (!oldRecords || oldRecords.length === 0) {
        return;
      }

      logger.info(`Cleaning up ${oldRecords.length} old QR records`);

      // Cleanup storage for old records
      for (const record of oldRecords) {
        if (record.qr_image_url) {
          try {
            const storagePath = `qr-codes/${record.session_id}.png`;
            await supabaseAdmin.storage
              .from('qr-codes')
              .remove([storagePath]);
          } catch (error: any) {
            logger.debug('Storage cleanup error for old record', { 
              sessionId: record.session_id,
              error: error.message 
            });
          }
        }
      }

      // Delete old records from database
      const { error: deleteError } = await supabaseAdmin
        .from('qr_codes')
        .delete()
        .eq('status', 'expired')
        .lt('updated_at', cutoffTime);

      if (deleteError) {
        logger.warn('Failed to delete old QR records', { error: deleteError.message });
      } else {
        logger.info(`Deleted ${oldRecords.length} old QR records`);
      }

    } catch (error: any) {
      logger.error('Failed to cleanup old records', { error: error.message });
    }
  }

  /**
   * Manual cleanup trigger
   */
  async manualCleanup(): Promise<{ success: boolean; message: string; stats?: any }> {
    try {
      const startTime = Date.now();
      
      await this.performCleanup();
      
      const duration = Date.now() - startTime;
      
      // Get current stats
      const { data: stats } = await supabaseAdmin
        .from('qr_codes')
        .select('status')
        .then(result => {
          if (result.data) {
            const statusCounts = result.data.reduce((acc: any, qr: any) => {
              acc[qr.status] = (acc[qr.status] || 0) + 1;
              return acc;
            }, {});
            return { data: statusCounts };
          }
          return { data: {} };
        });

      return {
        success: true,
        message: `Manual cleanup completed in ${duration}ms`,
        stats: {
          duration_ms: duration,
          qr_status_counts: stats || {},
          timestamp: new Date().toISOString()
        }
      };

    } catch (error: any) {
      return {
        success: false,
        message: `Manual cleanup failed: ${error.message}`
      };
    }
  }

  /**
   * Get cleanup service status
   */
  getStatus(): { isRunning: boolean; nextCleanup?: string } {
    return {
      isRunning: this.isRunning,
      nextCleanup: this.isRunning ? 
        new Date(Date.now() + 5 * 60 * 1000).toISOString() : 
        undefined
    };
  }
}

// Export singleton instance
export const qrCleanupService = new QRCleanupService();
