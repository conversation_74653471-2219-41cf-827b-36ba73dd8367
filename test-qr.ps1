# Test QR Management API

$baseUrl = "http://localhost:3355"
$tenantId = "9e20b074-5c04-4241-b2bd-58c0499c1f86"

# Test 1: Create QR Code
Write-Host "Testing QR Code Creation..." -ForegroundColor Yellow

$body = @{
    chatwoot_inbox_id = 281
    expires_in_minutes = 10
} | ConvertTo-Json

$headers = @{
    'X-Tenant-ID' = $tenantId
    'Content-Type' = 'application/json'
}

try {
    $response = Invoke-RestMethod -Uri "$baseUrl/api/qr-management/create" -Method POST -Headers $headers -Body $body
    Write-Host "✅ QR Code Created Successfully!" -ForegroundColor Green
    Write-Host "Account ID: $($response.data.account_id)" -ForegroundColor Cyan
    Write-Host "Session ID: $($response.data.session_id)" -ForegroundColor Cyan
    Write-Host "QR Code URL: $($response.data.qr_code_url)" -ForegroundColor Cyan
    Write-Host "Expires At: $($response.data.expires_at)" -ForegroundColor Cyan
    
    $sessionId = $response.data.session_id
    
    # Test 2: Check QR Status
    Write-Host "`nTesting QR Status Check..." -ForegroundColor Yellow
    
    $statusResponse = Invoke-RestMethod -Uri "$baseUrl/api/qr-management/status/$sessionId" -Method GET -Headers @{'X-Tenant-ID' = $tenantId}
    Write-Host "✅ QR Status Retrieved!" -ForegroundColor Green
    Write-Host "Status: $($statusResponse.data.status)" -ForegroundColor Cyan
    Write-Host "QR Image URL: $($statusResponse.data.qr_image_url)" -ForegroundColor Cyan
    
    # Test 3: List QR Codes
    Write-Host "`nTesting QR List..." -ForegroundColor Yellow
    
    $listResponse = Invoke-RestMethod -Uri "$baseUrl/api/qr-management/list" -Method GET -Headers @{'X-Tenant-ID' = $tenantId}
    Write-Host "✅ QR List Retrieved!" -ForegroundColor Green
    Write-Host "Total QR Codes: $($listResponse.data.qr_codes.Count)" -ForegroundColor Cyan
    
    # Test 4: Health Check
    Write-Host "`nTesting Health Check..." -ForegroundColor Yellow
    
    $healthResponse = Invoke-RestMethod -Uri "$baseUrl/health" -Method GET
    Write-Host "✅ Health Check Passed!" -ForegroundColor Green
    Write-Host "Status: $($healthResponse.status)" -ForegroundColor Cyan
    
    Write-Host "`n🎉 All tests passed!" -ForegroundColor Green
    
} catch {
    Write-Host "❌ Test Failed!" -ForegroundColor Red
    Write-Host "Error: $($_.Exception.Message)" -ForegroundColor Red
    
    if ($_.Exception.Response) {
        $reader = New-Object System.IO.StreamReader($_.Exception.Response.GetResponseStream())
        $responseBody = $reader.ReadToEnd()
        Write-Host "Response: $responseBody" -ForegroundColor Red
    }
}
