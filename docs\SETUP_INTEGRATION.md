# Hướng dẫn Setup Zalo Multi-Account Integration

## 🚀 Cấu hình Environment Variables

Thêm các biến môi trường sau vào file `.env`:

```env
# Supabase Configuration (đã có)
SUPABASE_URL=your-supabase-url
SUPABASE_ANON_KEY=your-anon-key
SUPABASE_SERVICE_ROLE_KEY=your-service-key

# Zalo Integration Configuration (mới)
DEFAULT_TENANT_ID=your-default-tenant-uuid
DEFAULT_CHATWOOT_INBOX_ID=your-default-inbox-id

# Optional: Monitoring
MONITORING_WEBHOOK_URL=your-webhook-url
SLACK_WEBHOOK_URL=your-slack-webhook
```

## 📊 Kiểm tra Database

Đảm bảo bảng `zalo_accounts` đã được tạo trong Supabase:

```sql
-- <PERSON>ể<PERSON> tra bảng tồn tại
SELECT table_name FROM information_schema.tables 
WHERE table_schema = 'public' AND table_name = 'zalo_accounts';

-- <PERSON><PERSON><PERSON> tra dữ liệu
SELECT id, tenant_id, zalo_user_id, status, chatwoot_inbox_id 
FROM zalo_accounts 
LIMIT 5;
```

## 🔧 Setup Workflow

### Bước 1: Cấu hình Default Values

```bash
# Set default tenant và inbox cho automatic migration
curl -X POST http://localhost:3355/api/zalo-integration/set-defaults \
  -H "Content-Type: application/json" \
  -d '{
    "tenant_id": "your-tenant-uuid",
    "chatwoot_inbox_id": 123
  }'
```

### Bước 2: Kiểm tra Integration Status

```bash
# Check integration health
curl http://localhost:3355/api/zalo-integration/health

# Check status for specific tenant
curl -H "X-Tenant-ID: your-tenant-uuid" \
  http://localhost:3355/api/zalo-integration/status
```

### Bước 3: Tạo QR Login mới (nếu cần)

```bash
# Tạo QR login với tenant và inbox ID
curl -X POST http://localhost:3355/api/zalo-integration/create-qr \
  -H "X-Tenant-ID: your-tenant-uuid" \
  -H "Content-Type: application/json" \
  -d '{
    "chatwoot_inbox_id": 123,
    "expires_in_minutes": 10
  }'
```

### Bước 4: Migrate Existing Login (tự động)

Khi server khởi động, nếu có session Zalo đang active, nó sẽ tự động được migrate vào database với default tenant và inbox ID.

## 🔄 API Endpoints Mới

### Integration Management

```
POST   /api/zalo-integration/create-qr        # Tạo QR với tenant + inbox
POST   /api/zalo-integration/set-defaults     # Set default values
GET    /api/zalo-integration/status           # Get integration status
POST   /api/zalo-integration/reload-accounts  # Reload từ database
GET    /api/zalo-integration/health           # Health check
```

### Existing Zalo Auth APIs

```
GET    /api/zalo-auth/accounts                # List accounts
GET    /api/zalo-auth/account/:id             # Account details
POST   /api/zalo-auth/refresh/:id             # Refresh session
GET    /api/zalo-monitoring/dashboard         # Monitoring dashboard
```

## 🧪 Testing Workflow

### Test 1: Kiểm tra Migration của Login hiện tại

1. **Start server** với existing Zalo login
2. **Check logs** để xem migration status:
   ```
   [INFO] Migrating Zalo login to database...
   [INFO] Successfully migrated login to database
   ```
3. **Verify database**:
   ```bash
   curl -H "X-Tenant-ID: your-tenant-uuid" \
     http://localhost:3355/api/zalo-integration/status
   ```

### Test 2: Tạo QR Login mới

1. **Tạo QR**:
   ```bash
   curl -X POST http://localhost:3355/api/zalo-integration/create-qr \
     -H "X-Tenant-ID: your-tenant-uuid" \
     -H "Content-Type: application/json" \
     -d '{"chatwoot_inbox_id": 123}'
   ```

2. **Lấy QR image**:
   ```bash
   # Sử dụng qr_code_url từ response trên
   curl http://localhost:3355/api/zalo-auth/qr-image/session-id
   ```

3. **Poll QR status**:
   ```bash
   curl -H "X-Tenant-ID: your-tenant-uuid" \
     http://localhost:3355/api/zalo-auth/qr-status/account-id
   ```

### Test 3: Kiểm tra Auto-recovery

1. **Restart server** để test load từ database
2. **Check logs** để xem loading process:
   ```
   [INFO] Loading existing accounts from database
   [INFO] Account initialized successfully
   ```
3. **Verify accounts** vẫn hoạt động:
   ```bash
   curl -H "X-Tenant-ID: your-tenant-uuid" \
     http://localhost:3355/api/zalo-auth/accounts
   ```

## 🔍 Troubleshooting

### Vấn đề 1: Migration không hoạt động

**Triệu chứng**: Existing login không được lưu vào database

**Giải pháp**:
1. Kiểm tra `DEFAULT_TENANT_ID` trong `.env`
2. Kiểm tra logs cho migration errors
3. Verify database permissions và RLS policies

### Vấn đề 2: Accounts không load sau restart

**Triệu chứng**: Server restart nhưng không có accounts nào được load

**Giải pháp**:
1. Kiểm tra database connection
2. Verify `zalo_accounts` table có data
3. Check account status (phải là 'active')
4. Kiểm tra auth_data có đầy đủ cookies không

### Vấn đề 3: QR Login không hoạt động

**Triệu chứng**: QR được tạo nhưng không login được

**Giải pháp**:
1. Kiểm tra QR expiry time
2. Verify tenant permissions
3. Check rate limiting
4. Kiểm tra zca-js version compatibility

## 📈 Monitoring

### Health Check Endpoints

```bash
# System health
curl http://localhost:3355/api/zalo-monitoring/health

# Integration health  
curl http://localhost:3355/api/zalo-integration/health

# Account metrics
curl -H "X-Tenant-ID: your-tenant-uuid" \
  http://localhost:3355/api/zalo-monitoring/dashboard
```

### Key Metrics to Monitor

- **Total Accounts**: Số lượng accounts trong database
- **Active Accounts**: Accounts đang hoạt động
- **Healthy Connections**: Connections không có lỗi
- **Response Time**: Thời gian phản hồi API
- **Error Rate**: Tỷ lệ lỗi

## 🎯 Production Checklist

### Before Deployment

- [ ] Environment variables configured
- [ ] Database schema deployed
- [ ] Default tenant/inbox set
- [ ] Health checks passing
- [ ] Rate limiting configured
- [ ] Monitoring alerts setup

### After Deployment

- [ ] Existing logins migrated successfully
- [ ] New QR logins working
- [ ] Auto-recovery after restarts
- [ ] Chatwoot integration functional
- [ ] Monitoring dashboards active

## 🔧 Advanced Configuration

### Custom Tenant/Inbox per Account

```javascript
// Frontend code để tạo QR cho specific tenant/inbox
const createQRLogin = async (tenantId, inboxId) => {
  const response = await fetch('/api/zalo-integration/create-qr', {
    method: 'POST',
    headers: {
      'X-Tenant-ID': tenantId,
      'Content-Type': 'application/json'
    },
    body: JSON.stringify({
      chatwoot_inbox_id: inboxId,
      expires_in_minutes: 10
    })
  });
  
  const result = await response.json();
  return result.data;
};
```

### Batch Account Management

```javascript
// Load multiple accounts
const loadAccounts = async (tenantId) => {
  const response = await fetch('/api/zalo-integration/reload-accounts', {
    method: 'POST',
    headers: {
      'X-Tenant-ID': tenantId
    }
  });
  
  return response.json();
};
```

### Real-time Status Updates

```javascript
// Poll account status
const pollAccountStatus = async (accountId, tenantId) => {
  const poll = setInterval(async () => {
    const response = await fetch(`/api/zalo-auth/account/${accountId}`, {
      headers: { 'X-Tenant-ID': tenantId }
    });
    
    const result = await response.json();
    
    if (result.data.status === 'active') {
      clearInterval(poll);
      console.log('Account is now active!');
    }
  }, 3000);
};
```

---

## ✅ Kết luận

Hệ thống integration đã sẵn sàng để:

1. **Tự động migrate** existing Zalo logins vào database
2. **Tạo QR logins mới** với tenant và inbox ID cụ thể  
3. **Auto-recovery** accounts từ database khi restart
4. **Multi-tenant support** với RLS security
5. **Real-time monitoring** và health checks
6. **Chatwoot integration** seamless

**Ready for production! 🚀**
