import { Router, Request, Response } from 'express';
import { asyncHand<PERSON> } from '../middleware/errorHandler';
import { AppError } from '../middleware/errorHandler';
import { validateTenant } from '../middleware/tenantMiddleware';
import { rateLimitQRGeneration } from '../middleware/zaloAuthMiddleware';
import { logger } from '../utils/logger';
import { zaloIntegrationService } from '../services/ZaloIntegrationService';
import { zaloAuthService } from '../services/ZaloAuthService';

const router = Router();

// POST /api/zalo-integration/create-qr - Tạo QR login với tenant và inbox ID
router.post('/create-qr', validateTenant, rateLimitQRGeneration, asyncHandler(async (req: Request, res: Response) => {
  const { chatwoot_inbox_id, expires_in_minutes = 10 } = req.body;
  const tenantId = req.tenantId!;

  logger.info('Create QR login with integration', {
    tenantId,
    chatwootInboxId: chatwoot_inbox_id,
    expiresInMinutes: expires_in_minutes,
    requestId: req.id
  });

  try {
    if (!chatwoot_inbox_id || typeof chatwoot_inbox_id !== 'number') {
      throw new AppError(
        'Chatwoot inbox ID is required and must be a number',
        400,
        'INVALID_CHATWOOT_INBOX_ID'
      );
    }

    logger.info('Calling zaloIntegrationService.createQRLogin', { tenantId, chatwoot_inbox_id, requestId: req.id });
    const result = await zaloIntegrationService.createQRLogin(tenantId, chatwoot_inbox_id);
    logger.info('zaloIntegrationService.createQRLogin completed', { result, requestId: req.id });

    if (!result.success) {
      throw new AppError(
        `Failed to create QR login: ${result.error}`,
        500,
        'QR_CREATION_FAILED'
      );
    }

    logger.info('Sending successful response', { accountId: result.accountId, requestId: req.id });
    res.json({
      success: true,
      message: 'QR login created successfully',
      data: {
        account_id: result.accountId,
        qr_code_url: result.qrCodeUrl,
        chatwoot_inbox_id: chatwoot_inbox_id,
        tenant_id: tenantId,
        expires_in_minutes,
        instructions: {
          step1: 'Display the QR code to user',
          step2: 'User scans QR code with Zalo app',
          step3: 'Poll QR status using account_id',
          step4: 'Account will be automatically linked to Chatwoot inbox when login completes'
        }
      }
    });

  } catch (error: any) {
    if (error instanceof AppError) {
      throw error;
    }

    logger.error('Failed to create integrated QR login', {
      tenantId,
      chatwootInboxId: chatwoot_inbox_id,
      error: error.message,
      requestId: req.id
    });

    throw new AppError(
      `Failed to create QR login: ${error.message}`,
      500,
      'QR_CREATION_FAILED'
    );
  }
}));

// POST /api/zalo-integration/migrate-existing - Migrate existing login to database
router.post('/migrate-existing', validateTenant, asyncHandler(async (req: Request, res: Response) => {
  const { chatwoot_inbox_id } = req.body;
  const tenantId = req.tenantId!;

  logger.info('Migrate existing login request', {
    tenantId,
    chatwootInboxId: chatwoot_inbox_id,
    requestId: req.id
  });

  try {
    // This endpoint is mainly for manual migration
    // The actual migration happens automatically when ZaloBot initializes
    res.json({
      success: true,
      message: 'Migration is handled automatically when Zalo bot initializes',
      data: {
        tenant_id: tenantId,
        chatwoot_inbox_id: chatwoot_inbox_id,
        note: 'If you have an active Zalo session, it will be automatically migrated to database on next server restart'
      }
    });

  } catch (error: any) {
    logger.error('Migration request failed', {
      tenantId,
      error: error.message,
      requestId: req.id
    });

    throw new AppError(
      `Migration request failed: ${error.message}`,
      500,
      'MIGRATION_FAILED'
    );
  }
}));

// GET /api/zalo-integration/status - Get integration status
router.get('/status', validateTenant, asyncHandler(async (req: Request, res: Response) => {
  const tenantId = req.tenantId!;

  try {
    // Get accounts for this tenant
    const accounts = await zaloAuthService.getAccountsByTenant(tenantId);
    
    // Get integration service defaults
    const defaults = zaloIntegrationService.getDefaults();

    // Calculate status
    const activeAccounts = accounts.filter(acc => acc.status === 'active');
    const linkedAccounts = accounts.filter(acc => acc.chatwoot_inbox_id);

    res.json({
      success: true,
      message: 'Integration status retrieved successfully',
      data: {
        tenant_id: tenantId,
        total_accounts: accounts.length,
        active_accounts: activeAccounts.length,
        linked_accounts: linkedAccounts.length,
        accounts: accounts.map(acc => ({
          id: acc.id,
          zalo_user_id: acc.zalo_user_id,
          zalo_display_name: acc.zalo_display_name,
          status: acc.status,
          chatwoot_inbox_id: acc.chatwoot_inbox_id,
          chatwoot_channel_status: acc.chatwoot_channel_status,
          login_method: acc.login_method,
          last_login_at: acc.last_login_at,
          last_activity_at: acc.last_activity_at
        })),
        defaults: {
          tenant_id: defaults.tenantId,
          inbox_id: defaults.inboxId
        },
        integration_ready: activeAccounts.length > 0
      }
    });

  } catch (error: any) {
    logger.error('Failed to get integration status', {
      tenantId,
      error: error.message
    });

    throw new AppError(
      `Failed to get integration status: ${error.message}`,
      500,
      'STATUS_FAILED'
    );
  }
}));

// POST /api/zalo-integration/set-defaults - Set default tenant and inbox for migrations
router.post('/set-defaults', asyncHandler(async (req: Request, res: Response) => {
  const { tenant_id, chatwoot_inbox_id } = req.body;

  if (!tenant_id) {
    throw new AppError('Tenant ID is required', 400, 'TENANT_ID_REQUIRED');
  }

  try {
    zaloIntegrationService.setDefaults(tenant_id, chatwoot_inbox_id);

    logger.info('Integration defaults updated', {
      tenantId: tenant_id,
      chatwootInboxId: chatwoot_inbox_id
    });

    res.json({
      success: true,
      message: 'Default values updated successfully',
      data: {
        tenant_id,
        chatwoot_inbox_id,
        note: 'These defaults will be used for automatic migrations when Zalo bot initializes'
      }
    });

  } catch (error: any) {
    logger.error('Failed to set defaults', {
      tenantId: tenant_id,
      error: error.message
    });

    throw new AppError(
      `Failed to set defaults: ${error.message}`,
      500,
      'SET_DEFAULTS_FAILED'
    );
  }
}));

// POST /api/zalo-integration/reload-accounts - Reload accounts from database
router.post('/reload-accounts', validateTenant, asyncHandler(async (req: Request, res: Response) => {
  const tenantId = req.tenantId!;

  logger.info('Reload accounts request', { tenantId, requestId: req.id });

  try {
    const result = await zaloIntegrationService.loadAccountsFromDatabase();

    res.json({
      success: true,
      message: 'Account reload completed',
      data: {
        loaded_count: result.loadedCount,
        error_count: result.errors.length,
        errors: result.errors,
        tenant_id: tenantId,
        reload_successful: result.success
      }
    });

  } catch (error: any) {
    logger.error('Failed to reload accounts', {
      tenantId,
      error: error.message,
      requestId: req.id
    });

    throw new AppError(
      `Failed to reload accounts: ${error.message}`,
      500,
      'RELOAD_FAILED'
    );
  }
}));

// GET /api/zalo-integration/health - Integration health check
router.get('/health', asyncHandler(async (req: Request, res: Response) => {
  try {
    const defaults = zaloIntegrationService.getDefaults();
    
    // Basic health check
    const isHealthy = defaults.tenantId !== null;

    res.json({
      success: true,
      message: isHealthy ? 'Integration service is healthy' : 'Integration service needs configuration',
      data: {
        status: isHealthy ? 'healthy' : 'needs_config',
        defaults: {
          tenant_id: defaults.tenantId,
          inbox_id: defaults.inboxId
        },
        configured: defaults.tenantId !== null,
        timestamp: new Date().toISOString()
      }
    });

  } catch (error: any) {
    logger.error('Integration health check failed', { error: error.message });

    res.status(503).json({
      success: false,
      message: 'Integration health check failed',
      error: {
        code: 'HEALTH_CHECK_FAILED',
        details: error.message
      },
      timestamp: new Date().toISOString()
    });
  }
}));

export default router;
